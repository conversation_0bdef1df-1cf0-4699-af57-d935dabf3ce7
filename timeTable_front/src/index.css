body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.app-container {
  min-height: 100vh;
  background-color: #f0f2f5;
}

/* Header 样式 */
.layout-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 24px;
  height: 45px !important;
  line-height: 45px !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  margin-left: 36px;
  display: flex;
  align-items: center;
  height: 45px;
  flex-shrink: 0;
}

.logo-img {
  height: 35px;
  max-width: 400px;
  object-fit: contain;
  cursor: pointer;
  transition: opacity 0.3s ease;
  border: none !important;
  box-shadow: none !important;
}

.logo-img:hover {
  opacity: 0.8;
}

.header-nav {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: space-between;
  margin-left: 24px;
  min-width: 0;
}

.nav-menu {
  border: none !important;
  flex: 1;
  max-width: 300px;
  overflow: visible !important;
}

.nav-menu .ant-menu-overflow {
  display: flex !important;
  width: 100% !important;
}

.nav-menu .ant-menu-overflow-item {
  flex-shrink: 0 !important;
  display: flex !important;
}

.nav-menu .ant-menu-overflow-item-rest {
  display: none !important;
}

.nav-menu .ant-menu-item {
  display: flex !important;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  flex-shrink: 0 !important;
}

.nav-menu .ant-menu-submenu-arrow,
.nav-menu .ant-menu-overflow-item-suffix {
  display: none !important;
}

.user-section {
  margin-left: 16px;
  flex-shrink: 0;
}

.user-dropdown {
  height: auto;
  padding: 4px 8px;
  display: flex;
  align-items: center;
}

.username-text {
  margin-left: 8px;
}

/* Dashboard 样式 */
.content-container {
  max-width: 1200px;
  margin: 24px auto;
  padding: 0 24px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  color: #262626;
}

.create-button {
  white-space: nowrap;
}

.create-button-text {
  margin-left: 4px;
}

.timetable-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.loading-container {
  text-align: center;
  padding: 50px 0;
}

.empty-state {
  padding: 40px 20px;
}

.timetable-list .timetable-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.timetable-list .timetable-item:last-child {
  border-bottom: none;
}

.timetable-avatar {
  background-color: #1890ff;
}

.timetable-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  text-align: left !important;
  display: block;
  margin-left: 0 !important;
  align-self: flex-start;
}

.timetable-description {
  color: #8c8c8c;
  font-size: 14px;
}

.timetable-type, .timetable-time, .timetable-created {
  margin-bottom: 4px;
}

.timetable-type {
  color: #1890ff;
  font-weight: 500;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

.admin-card {
  margin-top: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* 移动端适配 (768px 以下) */
@media (max-width: 768px) {
  .layout-header {
    padding: 0 16px;
    height: 67px !important;
    line-height: 67px !important;
  }

  .header-content {
    gap: 8px;
  }

  .logo {
    height: 67px;
  }

  .logo-img {
    max-height: 53px;
    max-width: 200px;
  }

  .header-nav {
    margin-left: 8px;
  }

  .nav-menu {
    font-size: 12px;
    max-width: none !important;
    min-width: 160px;
  }

  .nav-menu .ant-menu-item {
    padding: 0 6px !important;
    font-size: 12px;
    min-width: 60px;
  }

  .nav-menu .ant-menu-item .anticon {
    font-size: 14px;
    margin-right: 4px;
  }

  .content-container {
    margin: 16px auto;
    padding: 0 16px;
  }

  .dashboard-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
  }

  .page-title {
    font-size: 18px;
    text-align: left;
    flex-shrink: 0;
  }

  .create-button {
    height: 36px;
    font-size: 14px;
    padding: 0 12px;
    flex-shrink: 0;
  }

  .create-button-text {
    margin-left: 2px;
  }

  .timetable-card {
    margin: 0 -4px;
  }

  .timetable-list .timetable-item {
    padding: 12px 0;
  }

  .timetable-title {
    font-size: 15px;
  }

  .timetable-description {
    font-size: 13px;
  }

  .action-text {
    display: none;
  }

  .action-button {
    min-width: 40px;
    padding: 4px 8px;
  }
}

/* 小屏幕设备适配 (480px 以下) */
@media (max-width: 480px) {
  .layout-header {
    height: 60px !important;
    line-height: 60px !important;
    padding: 0 12px;
  }

  .header-content {
    gap: 6px;
  }

  .logo {
    height: 60px;
  }

  .logo-img {
    max-height: 47px;
    max-width: 160px;
  }

  .username-text {
    display: none;
  }

  .header-nav {
    margin-left: 6px;
  }

  .nav-menu {
    max-width: none !important;
    min-width: 140px;
    font-size: 11px;
  }

  .nav-menu .ant-menu-item {
    padding: 0 4px !important;
    font-size: 11px;
    min-width: 50px;
  }

  .nav-menu .ant-menu-item .anticon {
    font-size: 12px;
    margin-right: 2px;
  }

  .content-container {
    padding: 0 12px;
  }

  .page-title {
    font-size: 18px;
  }

  .create-button {
    height: 44px;
    font-size: 15px;
  }

  .create-button-text {
    margin-left: 2px;
  }

  .timetable-card {
    margin: 0 -8px;
    border-radius: 8px;
  }

  .timetable-list .timetable-item {
    padding: 10px 12px;
  }

  .timetable-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .timetable-title {
    font-size: 16px;
  }

  .timetable-description {
    font-size: 12px;
  }

  .action-button {
    min-width: 36px;
    padding: 2px 6px;
  }
}

/* 超小屏幕设备适配 (360px 以下) */
@media (max-width: 360px) {
  .layout-header {
    height: 53px !important;
    line-height: 53px !important;
    padding: 0 8px;
  }

  .header-content {
    gap: 4px;
  }

  .logo {
    height: 53px;
  }

  .logo-img {
    max-height: 40px;
    max-width: 120px;
  }

  .header-nav {
    margin-left: 4px;
  }

  .nav-menu {
    max-width: none !important;
    min-width: 120px;
    font-size: 10px;
  }

  .nav-menu .ant-menu-item {
    padding: 0 3px !important;
    font-size: 10px;
    min-width: 45px;
  }

  .nav-menu .ant-menu-item .anticon {
    font-size: 11px;
    margin-right: 1px;
  }

  .page-title {
    font-size: 16px;
  }

  .create-button {
    height: 40px;
    font-size: 14px;
  }

  .timetable-card {
    margin: 0 -4px;
  }

  .timetable-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .action-button {
    min-width: 32px;
    padding: 2px 4px;
  }
}

/* 其他通用样式 */
.voice-input-container {
  text-align: center;
  padding: 40px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.voice-button {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  font-size: 24px;
}

.timetable-container {
  background: #fff;
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.time-slot {
  padding: 8px;
  border: 1px solid #f0f0f0;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.time-slot.has-class {
  background: #e6f7ff;
  border-color: #91d5ff;
}

.student-name {
  font-size: 12px;
  padding: 2px 6px;
  background: #1890ff;
  color: white;
  border-radius: 4px;
  margin: 2px;
  display: inline-block;
}

/* 课表查看页面样式 */
.timetable-top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  gap: 6px;
}

.back-icon-button {
  position: absolute;
  left: 16px;
  top: 16px;
  width: 36px;
  height: 36px;
}

.timetable-main-title {
  font-size: 16px;
  margin: 0;
  text-align: center;
  flex: 1;
}

.edit-top-button {
  height: 28px;
  padding: 0 8px;
  font-size: 12px;
}

.timetable-header {
  flex-direction: column;
  align-items: stretch;
  gap: 12px;
  margin-bottom: 16px;
}

.timetable-title {
  font-size: 18px;
  text-align: center;
}

.back-button {
  align-self: flex-start;
}

.card-header {
  flex-direction: column;
  align-items: stretch;
  gap: 8px;
}

.edit-button {
  width: 100%;
}

.timetable-view-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.card-header-simple {
  display: flex;
  align-items: center;
}

.week-info {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

.edit-button {
  flex-shrink: 0;
}

.compact-timetable-container {
  overflow-x: auto;
}

.compact-timetable {
  min-width: 400px;
}

.compact-timetable .ant-table-thead > tr > th {
  padding: 6px 4px;
  font-size: 12px;
  text-align: center;
  background: #fafafa;
  border: 1px solid #e8e8e8;
}

.compact-timetable .ant-table-tbody > tr > td {
  padding: 2px;
  text-align: center;
  border: 1px solid #e8e8e8;
  vertical-align: middle;
}

/* 课程单元格（非时间列）无内边距，让背景色填充满整个单元格 */
.compact-timetable .ant-table-tbody > tr > td.timetable-day-column {
  padding: 0 !important;
}

/* 更具体的选择器确保样式生效 */
.compact-timetable-container .ant-table-tbody > tr > td.timetable-day-column {
  padding: 0 !important;
  margin: 0 !important;
}

/* 针对Ant Design表格的更强制性样式 */
.ant-table-tbody > tr > td.timetable-day-column {
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  vertical-align: top !important;
  position: relative !important;
}

/* 强制填充样式 */
.ant-table-tbody > tr > td.timetable-day-column > div {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

.student-container {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  margin: 0 !important;
  padding: 0 !important;
}

.student-item {
  flex: 1 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 强制覆盖所有可能的Ant Design样式 */
.ant-table-tbody > tr > td.timetable-day-column,
.ant-table-tbody > tr > td.timetable-day-column > *,
.ant-table-tbody > tr > td.timetable-day-column .ant-popover-open,
.ant-table-tbody > tr > td.timetable-day-column .ant-popover-open > *,
.ant-table-tbody > tr > td.timetable-day-column .student-container,
.ant-table-tbody > tr > td.timetable-day-column .student-item {
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

.timetable-time-column {
  background: #f5f5f5 !important;
  width: 40px;
  height: 30px;
}

.time-cell {
  font-size: 11px;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
}

.time-part {
  display: block;
}

.day-header {
  text-align: center;
}

.day-name {
  font-size: 12px;
  font-weight: 600;
  color: #262626;
}

.day-date {
  font-size: 10px;
  color: #666;
  font-weight: normal;
  margin-top: 2px;
}

.time-slot-compact {
  min-height: 35px;
  max-height: 35px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1px;
  overflow: hidden;
}

.student-tag-compact {
  font-size: 10px;
  padding: 1px 3px;
  background: #1890ff;
  color: white;
  border-radius: 3px;
  cursor: pointer;
  margin: 1px 0;
  max-width: 100%;
  text-align: center;
  line-height: 1.2;
  word-break: break-all;
  overflow: hidden;
  display: block;
}

.student-tag-compact:hover {
  background: #40a9ff;
}

.course-detail {
  font-size: 13px;
}

.course-detail div {
  margin-bottom: 4px;
}

.week-pagination {
  margin-top: 24px;
  text-align: center;
}

.info-card {
  margin-top: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.info-content {
  color: #666;
  font-size: 14px;
}

.info-content div {
  margin-bottom: 4px;
}

/* 移动端表格样式 */
@media (max-width: 768px) {
  .voice-button {
    width: 80px;
    height: 80px;
    font-size: 20px;
  }

  .voice-input-container {
    padding: 20px;
  }

  .ant-table {
    font-size: 12px;
  }

  .ant-table-thead > tr > th {
    padding: 8px 4px;
    font-size: 12px;
  }

  .ant-table-tbody > tr > td {
    padding: 8px 4px;
  }

  .time-slot {
    min-height: 50px;
    font-size: 11px;
  }

  .student-name {
    font-size: 10px;
    padding: 1px 4px;
    margin: 1px;
  }

  /* 课表查看页面移动端优化 */
  .timetable-top-header {
    margin-bottom: 12px;
    gap: 8px;
  }

  .back-icon-button {
    width: 36px;
    height: 36px;
  }

  .timetable-main-title {
    font-size: 16px;
    margin: 0;
    flex: 1;
    text-align: left;
  }

  .edit-top-button {
    height: 28px;
    padding: 0 8px;
    font-size: 12px;
  }

  .timetable-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    margin-bottom: 16px;
  }

  .timetable-title {
    font-size: 18px;
    text-align: center;
  }

  .back-button {
    align-self: flex-start;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .edit-button {
    width: 100%;
  }

  .compact-timetable {
    min-width: 380px;
  }

  .compact-timetable .ant-table-thead > tr > th {
    padding: 4px 2px;
    font-size: 11px;
  }

  .compact-timetable .ant-table-tbody > tr > td {
    padding: 1px;
  }

  .time-cell {
    font-size: 10px;
  }

  .day-name {
    font-size: 11px;
  }

  .day-date {
    font-size: 9px;
  }

  .time-slot-compact {
    min-height: 32px;
    max-height: 32px;
  }

  .student-tag-compact {
    font-size: 9px;
    padding: 1px 2px;
  }

  .info-content {
    font-size: 13px;
  }
}

/* 小屏幕设备适配 (480px 以下) */
@media (max-width: 480px) {
  .layout-header {
    height: 90px;
    padding: 0 12px;
  }

  .header-content {
    gap: 6px;
  }

  .logo {
    height: 90px;
  }

  .logo-img {
    max-height: 70px;
    max-width: 160px;
  }

  .username-text {
    display: none;
  }

  .header-nav {
    margin-left: 6px;
  }

  .nav-menu {
    max-width: none !important;
    min-width: 140px;
    font-size: 11px;
  }

  .nav-menu .ant-menu-item {
    padding: 0 4px !important;
    font-size: 11px;
    min-width: 50px;
  }

  .nav-menu .ant-menu-item .anticon {
    font-size: 12px;
    margin-right: 2px;
  }

  .content-container {
    padding: 0 12px;
  }

  .page-title {
    font-size: 18px;
  }

  .create-button {
    height: 44px;
    font-size: 15px;
  }

  .create-button-text {
    margin-left: 2px;
  }

  .timetable-card {
    margin: 0 -8px;
    border-radius: 8px;
  }

  .timetable-list .timetable-item {
    padding: 10px 12px;
  }

  .timetable-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .timetable-title {
    font-size: 16px;
  }

  .timetable-description {
    font-size: 12px;
  }

  .action-button {
    min-width: 36px;
    padding: 2px 6px;
  }

  /* 课表查看页面小屏优化 */
  .timetable-top-header {
    margin-bottom: 10px;
    gap: 6px;
  }

  .back-icon-button {
    width: 32px;
    height: 32px;
  }

  .timetable-main-title {
    font-size: 14px;
    margin: 0;
    flex: 1;
    text-align: left;
  }

  .edit-top-button {
    height: 26px;
    padding: 0 6px;
    font-size: 11px;
  }

  .compact-timetable {
    min-width: 360px;
  }

  .compact-timetable .ant-table-thead > tr > th {
    padding: 3px 1px;
    font-size: 10px;
  }

  .time-cell {
    font-size: 9px;
  }

  .day-name {
    font-size: 10px;
  }

  .day-date {
    font-size: 8px;
  }

  .time-slot-compact {
    min-height: 30px;
    max-height: 30px;
  }

  .student-tag-compact {
    font-size: 8px;
    padding: 1px;
  }
}

.text-submit-button {
  width: 100%;
  display: block;
  margin: 0 auto;
  white-space: normal;
  height: auto;
  padding: 10px 15px;
  line-height: 1.5;
}

/* Responsive styles for DatePicker popup */
.mobile-friendly-datepicker .ant-picker-dropdown {
  width: 100% !important;
  min-width: 280px !important;
  max-width: 100% !important;
}

/* 移动端DatePicker优化 */
@media (max-width: 768px) {
  .mobile-friendly-datepicker .ant-picker-dropdown {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    z-index: 9999 !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
    margin-top: 4px !important;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
  }
}

/* 小屏幕设备的DatePicker处理 */
@media (max-width: 480px) {
  .mobile-friendly-datepicker .ant-picker-dropdown {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    transform: none !important;
    max-height: 50vh !important;
    overflow-y: auto !important;
    margin-top: 4px !important;
  }
}

/* DatePicker输入框优化 */
.mobile-friendly-datepicker input {
  caret-color: transparent !important;
  cursor: pointer !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

.mobile-friendly-datepicker input:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px #1890ff40 !important;
}

/* DatePicker移动端触摸优化 */
@media (max-width: 768px) {
  .mobile-friendly-datepicker .ant-picker-cell {
    padding: 8px 0 !important;
    height: 40px !important;
    line-height: 24px !important;
  }

  .mobile-friendly-datepicker .ant-picker-cell-inner {
    min-height: 24px !important;
    line-height: 24px !important;
  }

  .mobile-friendly-datepicker .ant-picker-header {
    padding: 8px 12px !important;
  }

  .mobile-friendly-datepicker .ant-picker-header button {
    min-height: 32px !important;
    padding: 4px 8px !important;
  }
}

/* 保留原有的RangePicker样式以防其他地方使用 */
.mobile-friendly-rangepicker {
  width: var(--rangepicker-width, 100%) !important;
  min-width: var(--rangepicker-width, 100%) !important;
  max-width: var(--rangepicker-width, 100%) !important;
}

.mobile-friendly-rangepicker .ant-picker-dropdown {
  width: var(--rangepicker-width, 100%) !important;
  min-width: var(--rangepicker-width, 100%) !important;
  max-width: var(--rangepicker-width, 100%) !important;
  box-sizing: border-box !important;
}

.mobile-friendly-rangepicker .ant-picker-panel-container {
  width: var(--rangepicker-width, 100%) !important;
  min-width: var(--rangepicker-width, 100%) !important;
  max-width: var(--rangepicker-width, 100%) !important;
  box-sizing: border-box !important;
}

.mobile-friendly-rangepicker .ant-picker-panels {
  width: var(--rangepicker-width, 100%) !important;
  min-width: var(--rangepicker-width, 100%) !important;
  max-width: var(--rangepicker-width, 100%) !important;
  box-sizing: border-box !important;
  display: flex !important;
  justify-content: space-between !important;
}

/* 确保弹出框内容也遵循宽度限制 */
.mobile-friendly-rangepicker .ant-picker-panel {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.mobile-friendly-rangepicker .ant-picker-date-panel,
.mobile-friendly-rangepicker .ant-picker-time-panel {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* 强制设置弹出框宽度与触发器一致 */
.mobile-friendly-rangepicker .ant-picker-dropdown-range {
  width: var(--rangepicker-width, 100%) !important;
  min-width: var(--rangepicker-width, 100%) !important;
  max-width: var(--rangepicker-width, 100%) !important;
}

/* 针对不同屏幕尺寸的特殊处理 */
.mobile-friendly-rangepicker .ant-picker-range-wrapper {
  width: 100% !important;
}

/* 移动端特殊处理 */
@media (max-width: 768px) {
  .mobile-friendly-rangepicker .ant-picker-panels {
    flex-direction: column;
    width: 100% !important;
  }

  .mobile-friendly-rangepicker .ant-picker-panel {
    width: 100% !important;
    max-width: 100% !important;
  }

  .mobile-friendly-rangepicker .ant-picker-date-panel {
    width: 100% !important;
  }

  /* 移动端日期选择器弹出层优化 - 在输入框下方显示 */
  .mobile-friendly-rangepicker .ant-picker-dropdown {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    z-index: 9999 !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
    margin-top: 4px !important;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
  }
}

@media (max-width: 480px) {
  .mobile-friendly-rangepicker .ant-picker-panels {
    flex-direction: column;
  }

  .mobile-friendly-rangepicker .ant-picker-panel {
    width: 100% !important;
  }

  /* 小屏幕设备的特殊处理 */
  .mobile-friendly-rangepicker .ant-picker-dropdown {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    transform: none !important;
    max-height: 50vh !important;
    overflow-y: auto !important;
    margin-top: 4px !important;
  }
}

/* 防止键盘弹出时的样式问题 */
.mobile-friendly-rangepicker input {
  caret-color: transparent !important;
  cursor: pointer !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

.mobile-friendly-rangepicker input:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px #1890ff40 !important;
}

/* 确保弹出层在移动端正确显示 */
.mobile-friendly-rangepicker .ant-picker-panel-container {
  background: #fff !important;
  border-radius: 6px !important;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
}

/* 移动端触摸优化 */
@media (max-width: 768px) {
  .mobile-friendly-rangepicker .ant-picker-cell {
    padding: 8px 0 !important;
    height: 40px !important;
    line-height: 24px !important;
  }

  .mobile-friendly-rangepicker .ant-picker-cell-inner {
    min-height: 24px !important;
    line-height: 24px !important;
  }

  .mobile-friendly-rangepicker .ant-picker-header {
    padding: 8px 12px !important;
  }

  .mobile-friendly-rangepicker .ant-picker-header button {
    min-height: 32px !important;
    padding: 4px 8px !important;
  }
}