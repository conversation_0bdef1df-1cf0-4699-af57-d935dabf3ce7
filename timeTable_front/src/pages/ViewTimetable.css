/* ViewTimetable.css */

.compact-timetable-container {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin: 4px;
}

.compact-timetable .ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.compact-timetable .ant-table-thead > tr > th {
  background-color: #fafafa !important;
  font-weight: bold;
  text-align: center;
}

.timetable-time-column .time-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 12px;
  line-height: 1.2;
}

.day-header {
  text-align: center;
}

.day-name {
  font-weight: bold;
}

.day-date {
  font-size: 12px;
  color: #888;
}

.timetable-day-column {
  padding: 0 !important; /* Override default Ant Design cell padding */
} 