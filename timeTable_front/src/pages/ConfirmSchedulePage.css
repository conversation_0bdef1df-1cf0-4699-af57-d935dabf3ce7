.schedule-table-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; /* 优化在iOS上的滚动体验 */
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin: 0 5px;
  margin-bottom: 2rem;
}

.schedule-table {
  width: 100%;
  table-layout: fixed; /* 让列宽更可控 */
  border-collapse: collapse;
}

.schedule-table th,
.schedule-table td {
  border: 1px solid #ddd;
  padding: 8px 4px; /* 减小内边距 */
  text-align: center;
  vertical-align: middle;
  word-break: break-all; /* 允许单词内换行 */
}

.schedule-table th {
  background-color: #fafafa;
  font-weight: bold;
}

.schedule-table input[type="text"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  text-align: center;
}

.schedule-table .action-cell {
  width: 60px; /* 减小操作列宽度 */
}

.action-cell {
  text-align: center;
} 