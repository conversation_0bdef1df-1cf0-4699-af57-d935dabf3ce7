# -*- coding: utf-8 -*-

import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from datetime import date, timedelta
import locale
import math

def create_schedule_excel():
    """
    生成一个包含多位学生暑期课程的Excel时间表。
    按周分组显示，每周一个独立的表格区域。
    """
    # --- 1. 数据定义 (Data Definition) ---
    # 使用一个列表来存储所有的课程安排
    schedules = []
    
    # 梁诗阳的课程安排
    # 7月7日-11日
    schedules.extend([{'name': '梁诗阳', 'date': date(2025, 7, d), 'time': 15} for d in range(7, 12)])
    # 7月22日-24日
    schedules.extend([{'name': '梁诗阳', 'date': date(2025, 7, d), 'time': 15} for d in range(22, 25)])
    # 8月9日-15日
    schedules.extend([{'name': '梁诗阳', 'date': date(2025, 8, d), 'time': 15} for d in range(9, 16)])
    # 8月25日-30日
    schedules.extend([{'name': '梁诗阳', 'date': date(2025, 8, d), 'time': 15} for d in range(25, 31)])

    # 马欣然的课程安排
    # 7月18日-31日
    schedules.extend([{'name': '马欣然', 'date': date(2025, 7, d), 'time': 18} for d in range(18, 32)])

    # 大课安排：7月7号到7月11号，17:00~19:00，三个学生
    # 7月7日-11日，17:00~18:00时段
    schedules.extend([{'name': '曹谦悦\n张艺晗\n费琮言', 'date': date(2025, 7, d), 'time': 17} for d in range(7, 12)])
    # 7月7日-11日，18:00~19:00时段
    schedules.extend([{'name': '曹谦悦\n张艺晗\n费琮言', 'date': date(2025, 7, d), 'time': 18} for d in range(7, 12)])

    # 孙韩旭的课程安排：周二周四中午12:00~13:00
    start_date = date(2025, 6, 30)
    end_date = date(2025, 8, 30)
    current_date = start_date
    while current_date <= end_date:
        # 周二是weekday()=1，周四是weekday()=3
        if current_date.weekday() in [1, 3]:  # 周二和周四
            schedules.append({'name': '孙韩旭', 'date': current_date, 'time': 12})
        current_date += timedelta(days=1)

    # 高悦的课程安排：周三19:20（使用19时段）
    current_date = start_date
    while current_date <= end_date:
        # 周三是weekday()=2
        if current_date.weekday() == 2:  # 周三
            schedules.append({'name': '高悦', 'date': current_date, 'time': 19})
        current_date += timedelta(days=1)

    # 于乐之的课程安排
    specific_dates = [
        (date(2025, 7, 4), 19),
        (date(2025, 7, 14), 17),
        (date(2025, 7, 18), 17),
        (date(2025, 7, 21), 17),
        (date(2025, 7, 25), 17),
        (date(2025, 7, 28), 17),
        (date(2025, 8, 1), 17),
        (date(2025, 8, 4), 17),
        (date(2025, 8, 8), 17),
        (date(2025, 8, 11), 17)
    ]
    for d, t in specific_dates:
        schedules.append({'name': '于乐之', 'date': d, 'time': t})

    # 将课程数据转换为更易于查询的字典格式，键为 (日期, 小时)
    schedule_map = {(s['date'], s['time']): s['name'] for s in schedules}

    # --- 2. 按周分组日期 (Group dates by week) ---
    def get_week_dates(start_date, end_date):
        """按周分组日期，每周从周一开始"""
        weeks = []
        current = start_date
        
        # 找到第一个周一
        while current.weekday() != 0:  # 0 = 周一
            current -= timedelta(days=1)
        
        while current <= end_date:
            week_start = current
            week_end = min(current + timedelta(days=6), end_date)  # 周日或结束日期
            
            # 只包含在范围内的日期
            week_dates = []
            date_iter = max(week_start, start_date)
            while date_iter <= min(week_end, end_date):
                week_dates.append(date_iter)
                date_iter += timedelta(days=1)
            
            if week_dates:  # 只有当周有日期时才添加
                weeks.append(week_dates)
            
            current += timedelta(days=7)
        
        return weeks

    weeks = get_week_dates(start_date, end_date)

    # --- 3. 创建Excel工作簿和工作表 (Create Excel Workbook and Sheet) ---
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "暑期课程总表"

    # --- 4. 定义样式 (Define Styles) ---
    # 表头样式
    header_font = Font(name='Arial', size=12, bold=True)
    header_alignment = Alignment(horizontal='center', vertical='center')
    header_fill = PatternFill(start_color='DDEBF7', end_color='DDEBF7', fill_type='solid')

    # 周标题样式 - 更突出
    week_header_font = Font(name='Arial', size=16, bold=True, color='FFFFFF')
    week_header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')

    # 单元格通用样式
    cell_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
    thick_border = Border(left=Side(style='thick'), right=Side(style='thick'), top=Side(style='thick'), bottom=Side(style='thick'))
    
    # 学生颜色样式 - 更亮的颜色配置
    student_fills = {
        '梁诗阳': PatternFill(start_color='7FFF00', end_color='7FFF00', fill_type='solid'),  # 亮绿色
        '马欣然': PatternFill(start_color='FF6347', end_color='FF6347', fill_type='solid'),   # 亮橙红色
        '曹谦悦\n张艺晗\n费琮言': PatternFill(start_color='00BFFF', end_color='00BFFF', fill_type='solid'),  # 亮蓝色（大课）
        '孙韩旭': PatternFill(start_color='FFD700', end_color='FFD700', fill_type='solid'),  # 金色
        '高悦': PatternFill(start_color='FF69B4', end_color='FF69B4', fill_type='solid'),    # 亮粉色
        '于乐之': PatternFill(start_color='F0E68C', end_color='F0E68C', fill_type='solid')   # 卡其色
    }
    student_font = Font(name='Arial', size=11, bold=True)  # 黑色字体
    
    # 设置星期几的显示语言为中文
    try:
        locale.setlocale(locale.LC_TIME, 'zh_CN.UTF-8')
    except locale.Error:
        print("警告：无法设置中文locale。星期几可能以英文显示。")

    # --- 5. 定义时间段 (Define time slots) ---
    time_slots = []
    for h in range(9, 20):
        if h == 19:
            time_slots.append(f"{h:02}:20 - {h+1:02}:00")  # 19:20-20:00 为高悦课程
        else:
            time_slots.append(f"{h:02}:00 - {h+1:02}:00")

    # --- 6. 按周生成表格 (Generate tables by week) ---
    current_row = 1
    
    for week_index, week_dates in enumerate(weeks):
        # 在每周之间添加空行分隔（除了第一周）
        if week_index > 0:
            current_row += 2
        
        # 周标题
        week_start_str = week_dates[0].strftime('%m月%d日')
        week_end_str = week_dates[-1].strftime('%m月%d日')
        week_title = f"第{week_index + 1}周 ({week_start_str} - {week_end_str})"
        
        # 合并单元格作为周标题
        week_title_end_col = len(week_dates) + 1
        ws.merge_cells(start_row=current_row, start_column=1, end_row=current_row, end_column=week_title_end_col)
        week_cell = ws.cell(row=current_row, column=1, value=week_title)
        week_cell.font = week_header_font
        week_cell.alignment = header_alignment
        week_cell.fill = week_header_fill
        week_cell.border = thick_border
        
        # 设置周标题行高
        ws.row_dimensions[current_row].height = 25
        current_row += 1
        
        # 设置时间列 (A列)
        ws.cell(row=current_row, column=1, value="时间").font = header_font
        ws.cell(row=current_row, column=1).alignment = header_alignment
        ws.cell(row=current_row, column=1).fill = header_fill
        ws.column_dimensions['A'].width = 15

        # 设置日期行
        for col_index, week_date in enumerate(week_dates, start=2):
            day_of_week_str = week_date.strftime('%a').replace('Mon', '一').replace('Tue', '二').replace('Wed', '三').replace('Thu', '四').replace('Fri', '五').replace('Sat', '六').replace('Sun', '日')
            date_header = f"{week_date.month}/{week_date.day}\n({day_of_week_str})"
            
            cell = ws.cell(row=current_row, column=col_index, value=date_header)
            cell.font = header_font
            cell.alignment = header_alignment
            cell.fill = header_fill
            ws.column_dimensions[openpyxl.utils.get_column_letter(col_index)].width = 12

        current_row += 1
        
        # 填充时间段和课程内容
        for time_index, time_slot in enumerate(time_slots):
            time_hour = int(time_slot.split(':')[0])
            
            # 时间列
            ws.cell(row=current_row + time_index, column=1, value=time_slot).alignment = cell_alignment
            
            # 各日期列的课程内容
            for col_index, week_date in enumerate(week_dates, start=2):
                if (week_date, time_hour) in schedule_map:
                    student_name = schedule_map[(week_date, time_hour)]
                    cell = ws.cell(row=current_row + time_index, column=col_index, value=student_name)
                    cell.font = student_font
                    cell.alignment = cell_alignment
                    if student_name in student_fills:
                        cell.fill = student_fills[student_name]
        
        # 处理12:00-14:00合并单元格
        time_12_row = None
        time_13_row = None
        for i, time_slot in enumerate(time_slots):
            if time_slot.startswith("12:00"):
                time_12_row = current_row + i
            elif time_slot.startswith("13:00"):
                time_13_row = current_row + i
        
        if time_12_row and time_13_row:
            # 合并时间列的12:00和13:00单元格
            ws.merge_cells(start_row=time_12_row, start_column=1, end_row=time_13_row, end_column=1)
            ws.cell(row=time_12_row, column=1, value="12:00 - 14:00").alignment = cell_alignment
            
            # 合并各日期列的12:00和13:00单元格（如果没有孙韩旭的课程）
            for col_index, week_date in enumerate(week_dates, start=2):
                if week_date.weekday() not in [1, 3]:  # 不是周二或周四
                    ws.merge_cells(start_row=time_12_row, start_column=col_index, end_row=time_13_row, end_column=col_index)
        
        current_row += len(time_slots)

    # --- 7. 应用边框 (Apply Borders) ---
    for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
        for cell in row:
            if cell.value is not None:  # 只给有内容的单元格加边框
                cell.border = thin_border
            
    # --- 8. 保存文件 (Save the File) ---
    file_name = "暑期课程表.xlsx"
    wb.save(file_name)
    print(f"成功！课程表已保存为 '{file_name}'")
    print("表格已按周分组显示，向下滚动可以看到不同的周！")

# --- 运行程序 (Run the script) ---
if __name__ == "__main__":
    create_schedule_excel()
