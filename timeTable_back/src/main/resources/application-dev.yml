server:
  port: 8089
  servlet:
    context-path: /timetable/api

  datasource:
    url: ****************************************************************************************************************************************************
    username: timetable_dev
    password: Le<PERSON>i*0217
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

