server:
  servlet:
    context-path: /timetable/api

spring:
  application:
    name: timetable-backend
  profiles:
    active: dev

  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console

  # jOOQ配置（由Spring Boot自动配置）
  sql:
    init:
      mode: always
      # schema-locations: classpath:schema.sql

  flyway:
    enabled: false

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# JWT配置
jwt:
  secret: timetableSecretKey2024ForVoiceSchedulingSystem
  expiration: 86400000 # 24小时

# AI API配置 - 请根据实际情况修改
ai:
  # 语音识别API配置
  speech:
    provider: siliconFlow # 可选: openai, baidu, tencent, aliyun
    api-url: https://api.siliconflow.cn/v1
    api-key: sk-vsowhlqsbsbuvublbvizaiwdkydooizbzlbzyfuolyyhcbri

  # 自然语言处理API配置
  nlp:
    provider: siliconFlow # 可选: openai, baidu, tencent, aliyun
    api-url: https://api.siliconflow.cn/v1
    api-key: sk-vsowhlqsbsbuvublbvizaiwdkydooizbzlbzyfuolyyhcbri
    model: deepseek-ai/DeepSeek-R1-Distill-Qwen-7B

# 日志配置
logging:
  level:
    com.timetable: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/timetable.log

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: ************************************************************************************************************************************************
    username: timetable_dev
    password: Leilei*0217
  jpa:
    show-sql: true

logging:
  level:
    com.timetable: DEBUG

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: ************************************************************************************************************************************************
    username: timetable
    password: Leilei*0217
  jpa:
    show-sql: true

logging:
  level:
    com.timetable: INFO

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: ************************************************************************************************************************************************
    username: timetable
    password: Leilei*0217
  jpa:
    show-sql: false

logging:
  level:
    com.timetable: INFO
    root: WARN
