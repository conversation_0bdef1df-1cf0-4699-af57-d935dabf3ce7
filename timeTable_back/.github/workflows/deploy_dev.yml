name: deploy_dev_back
on:
  push:
    branches:
      - dev

jobs:
  dev-timeTable-back:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: JDK 8
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Cache Maven packages
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2
      - name: Flyway Clean
        if: ${{ vars.FLYWAY_CLEAN == '1' }}
        run: mvn flyway:clean -P dev
      - name: Build application lesson_web_back
        run: mvn flyway:repair -P dev && mvn flyway:migrate -P dev && mvn jooq-codegen:generate -P dev && mvn clean compile -P dev && mvn install -P dev -Dmaven.test.skip=true
      - name: Scp jar To HW Server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HW_TEST_SERVER }}
          username: ${{ secrets.HW_SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          source: ${{ github.workspace }}/target/timetable-backend-dev.jar
          target: /root/lesson
          strip_components: 6
      - name: Run Java Service Of timetable-back on HW Server
        uses: fifsky/ssh-action@master
        with:
          command: |
            # 设置环境变量指定Spring profile
            export SPRING_PROFILES_ACTIVE=dev
            supervisorctl status timetable-back-dev | awk '{print $2}' | while read line; do 
              if [ $line == 'RUNNING' ]; then 
                supervisorctl restart timetable-back-dev
              else 
                supervisorctl start timetable-back-dev
              fi 
            done
          host: ${{ secrets.HW_TEST_SERVER }}
          user: ${{ secrets.HW_SERVER_USERNAME }}
          pass: ${{ secrets.HW_SERVER_PWD }}
          args: "-tt" 