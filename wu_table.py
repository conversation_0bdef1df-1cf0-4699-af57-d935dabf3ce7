# -*- coding: utf-8 -*-

import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from datetime import date, timedelta
import locale

def create_coach_schedule_excel():
    """
    生成教练课程时间表的Excel文件。
    横坐标是日期，纵坐标是时间段。
    """
    # --- 1. 数据定义 (Data Definition) ---
    schedules = []
    
    # 定义开始和结束日期
    start_date = date(2025, 6, 30)
    end_date = date(2025, 8, 31)
    
    # 生成每日课程安排
    current_date = start_date
    while current_date <= end_date:
        weekday = current_date.weekday()  # 0=周一, 1=周二, ..., 6=周日
        
        # 周一、三、五的课程安排
        if weekday in [0, 2, 4]:  # 周一、三、五
            schedules.append({'name': '王宇阳', 'date': current_date, 'time': 15})
            schedules.append({'name': '陈翰骁', 'date': current_date, 'time': 17})
        
        # 周二、四、六的课程安排
        elif weekday in [1, 3, 5]:  # 周二、四、六
            schedules.append({'name': '杨潇钧', 'date': current_date, 'time': 15})
        
        # 7月7日~17日的特殊安排：16:00~17:00 潘陆洋
        if date(2025, 7, 7) <= current_date <= date(2025, 7, 17):
            schedules.append({'name': '潘陆洋', 'date': current_date, 'time': 16})
        
        # 7月18日~31日的特殊安排：18:00~19:00 朴泰宇
        if date(2025, 7, 18) <= current_date <= date(2025, 7, 31):
            schedules.append({'name': '朴泰宇', 'date': current_date, 'time': 18})

        # 7月2日~18日的偶数日期：18:00~19:00 孙怡然
        if date(2025, 7, 2) <= current_date <= date(2025, 7, 18) and current_date.day % 2 == 0:
            schedules.append({'name': '孙怡然', 'date': current_date, 'time': 18})

        # 每周二、四、六、日的下午5-6点：陈若卿
        if weekday in [1, 3, 5, 6]:  # 周二、四、六、日
            schedules.append({'name': '陈若卿', 'date': current_date, 'time': 17})
        
        # 7月12日的特殊安排：18:00~19:00 纪承佑
        if current_date == date(2025, 7, 12):
            schedules.append({'name': '纪承佑', 'date': current_date, 'time': 18})
        
        current_date += timedelta(days=1)

    # 将课程数据转换为更易于查询的字典格式，键为 (日期, 小时)
    schedule_map = {(s['date'], s['time']): s['name'] for s in schedules}

    # --- 2. 按周分组日期 (Group dates by week) ---
    def get_week_dates(start_date, end_date):
        """按周分组日期，每周从周一开始"""
        weeks = []
        current = start_date
        
        # 找到第一个周一
        while current.weekday() != 0:  # 0 = 周一
            current -= timedelta(days=1)
        
        while current <= end_date:
            week_start = current
            week_end = min(current + timedelta(days=6), end_date)  # 周日或结束日期
            
            # 只包含在范围内的日期
            week_dates = []
            date_iter = max(week_start, start_date)
            while date_iter <= min(week_end, end_date):
                week_dates.append(date_iter)
                date_iter += timedelta(days=1)
            
            if week_dates:  # 只有当周有日期时才添加
                weeks.append(week_dates)
            
            current += timedelta(days=7)
        
        return weeks

    weeks = get_week_dates(start_date, end_date)

    # --- 3. 创建Excel工作簿和工作表 (Create Excel Workbook and Sheet) ---
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "教练课程总表"

    # --- 4. 定义样式 (Define Styles) ---
    # 表头样式
    header_font = Font(name='Arial', size=12, bold=True)
    header_alignment = Alignment(horizontal='center', vertical='center')
    header_fill = PatternFill(start_color='DDEBF7', end_color='DDEBF7', fill_type='solid')

    # 周标题样式 - 更突出
    week_header_font = Font(name='Arial', size=16, bold=True, color='FFFFFF')
    week_header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')

    # 单元格通用样式
    cell_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
    
    # 教练颜色样式 - 更亮的颜色配置
    coach_fills = {
        '王宇阳': PatternFill(start_color='FF6B6B', end_color='FF6B6B', fill_type='solid'),  # 亮红色
        '陈翰骁': PatternFill(start_color='4ECDC4', end_color='4ECDC4', fill_type='solid'),  # 亮青蓝色
        '杨潇钧': PatternFill(start_color='FFEB3B', end_color='FFEB3B', fill_type='solid'),  # 亮黄色
        '潘陆洋': PatternFill(start_color='E91E63', end_color='E91E63', fill_type='solid'),  # 亮紫红色
        '朴泰宇': PatternFill(start_color='FF9800', end_color='FF9800', fill_type='solid'),   # 亮橙色
        '孙怡然': PatternFill(start_color='8A2BE2', end_color='8A2BE2', fill_type='solid'),   # 蓝紫色
        '陈若卿': PatternFill(start_color='00FF00', end_color='00FF00', fill_type='solid')   # 亮绿色
    }
    coach_font = Font(name='Arial', size=11, bold=True)  # 黑色字体
    
    # 设置星期几的显示语言为中文
    try:
        locale.setlocale(locale.LC_TIME, 'zh_CN.UTF-8')
    except locale.Error:
        print("警告：无法设置中文locale。星期几可能以英文显示。")

    # --- 5. 定义时间段 (Define time slots) ---
    time_slots = [f"{h:02}:00 - {h+1:02}:00" for h in range(9, 20)]
    time_map = {int(time_str.split(':')[0]): i for i, time_str in enumerate(time_slots, start=2)}

    # --- 6. 按周生成表格 (Generate tables by week) ---
    current_row = 1
    
    for week_index, week_dates in enumerate(weeks):
        # 在每周之间添加空行分隔（除了第一周）
        if week_index > 0:
            current_row += 2
        
        # 周标题
        week_start_str = week_dates[0].strftime('%m月%d日')
        week_end_str = week_dates[-1].strftime('%m月%d日')
        week_title = f"第{week_index + 1}周 ({week_start_str} - {week_end_str})"
        
        # 合并单元格作为周标题
        week_title_end_col = len(week_dates) + 1
        ws.merge_cells(start_row=current_row, start_column=1, end_row=current_row, end_column=week_title_end_col)
        week_cell = ws.cell(row=current_row, column=1, value=week_title)
        week_cell.font = week_header_font
        week_cell.alignment = header_alignment
        week_cell.fill = week_header_fill
        
        # 设置周标题行高
        ws.row_dimensions[current_row].height = 25
        current_row += 1
        
        # 设置时间列 (A列)
        ws.cell(row=current_row, column=1, value="时间").font = header_font
        ws.cell(row=current_row, column=1).alignment = header_alignment
        ws.cell(row=current_row, column=1).fill = header_fill
        ws.column_dimensions['A'].width = 15

        # 设置日期行
        for col_index, week_date in enumerate(week_dates, start=2):
            day_of_week_str = week_date.strftime('%a').replace('Mon', '一').replace('Tue', '二').replace('Wed', '三').replace('Thu', '四').replace('Fri', '五').replace('Sat', '六').replace('Sun', '日')
            date_header = f"{week_date.month}/{week_date.day}\n({day_of_week_str})"
            
            cell = ws.cell(row=current_row, column=col_index, value=date_header)
            cell.font = header_font
            cell.alignment = header_alignment
            cell.fill = header_fill
            ws.column_dimensions[openpyxl.utils.get_column_letter(col_index)].width = 12

        current_row += 1
        
        # 填充时间段和课程内容
        for time_index, time_slot in enumerate(time_slots):
            time_hour = int(time_slot.split(':')[0])
            
            # 时间列
            ws.cell(row=current_row + time_index, column=1, value=time_slot).alignment = cell_alignment
            
            # 各日期列的课程内容
            for col_index, week_date in enumerate(week_dates, start=2):
                if (week_date, time_hour) in schedule_map:
                    coach_name = schedule_map[(week_date, time_hour)]
                    cell = ws.cell(row=current_row + time_index, column=col_index, value=coach_name)
                    cell.font = coach_font
                    cell.alignment = cell_alignment
                    if coach_name in coach_fills:
                        cell.fill = coach_fills[coach_name]
        
        # 处理12:00-14:00合并单元格
        time_12_row = None
        time_13_row = None
        for i, time_slot in enumerate(time_slots):
            if time_slot.startswith("12:00"):
                time_12_row = current_row + i
            elif time_slot.startswith("13:00"):
                time_13_row = current_row + i
        
        if time_12_row and time_13_row:
            # 合并时间列的12:00和13:00单元格
            ws.merge_cells(start_row=time_12_row, start_column=1, end_row=time_13_row, end_column=1)
            ws.cell(row=time_12_row, column=1, value="12:00 - 14:00").alignment = cell_alignment
            
            # 合并各日期列的12:00和13:00单元格
            for col_index, week_date in enumerate(week_dates, start=2):
                ws.merge_cells(start_row=time_12_row, start_column=col_index, end_row=time_13_row, end_column=col_index)
        
        current_row += len(time_slots)

    # --- 7. 应用边框 (Apply Borders) ---
    for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
        for cell in row:
            if cell.value is not None:  # 只给有内容的单元格加边框
                cell.border = thin_border
                
    # --- 8. 保存文件 (Save the File) ---
    file_name = "教练课程表.xlsx"
    wb.save(file_name)
    print(f"成功！教练课程表已保存为 '{file_name}'")

# --- 运行程序 (Run the script) ---
if __name__ == "__main__":
    create_coach_schedule_excel() 